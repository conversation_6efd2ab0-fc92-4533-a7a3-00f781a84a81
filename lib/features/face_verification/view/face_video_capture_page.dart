import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:bloomg_flutter/core/di/injection.dart';
import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/camera_preview_widget.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/countdown_timer_widget.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/recording_feedback_widget.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/result_screen_widget.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:responsive_framework/responsive_framework.dart';

/// {@template face_video_capture_page}
/// Main page for face verification video capture.
///
/// Provides a full-screen camera interface with face detection guidance,
/// countdown timer, recording feedback, and result screens.
/// {@endtemplate}
class FaceVideoCapturePage extends StatefulWidget {
  /// {@macro face_video_capture_page}
  const FaceVideoCapturePage({super.key});

  @override
  State<FaceVideoCapturePage> createState() => _FaceVideoCapturePageState();
}

class _FaceVideoCapturePageState extends State<FaceVideoCapturePage>
    with WidgetsBindingObserver {
  final LoggerService _logger = LoggerService();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face video capture page initialized',
      ),
    );
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face video capture page disposed',
      ),
    );

    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Handle app lifecycle changes for camera management
    switch (state) {
      case AppLifecycleState.paused:
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'App paused - disposing camera resources',
          ),
        );
        context.read<FaceVideoCaptureBloc>().add(const DisposeResources());
      case AppLifecycleState.resumed:
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'App resumed - reinitializing camera',
          ),
        );
        context.read<FaceVideoCaptureBloc>().add(const InitializeCamera());
      default:
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          getIt<FaceVideoCaptureBloc>()..add(const InitializeCamera()),
      child: ResponsiveBreakpoints.builder(
        child: Scaffold(
          backgroundColor: Colors.black,
          body: SafeArea(
            child: BlocConsumer<FaceVideoCaptureBloc, FaceVideoCaptureState>(
              listener: _handleStateChanges,
              builder: _buildContent,
            ),
          ),
        ),
        breakpoints: const [
          Breakpoint(start: 0, end: 450, name: MOBILE),
          Breakpoint(start: 451, end: 800, name: TABLET),
          Breakpoint(start: 801, end: 1920, name: DESKTOP),
          Breakpoint(start: 1921, end: double.infinity, name: '4K'),
        ],
      ),
    );
  }

  /// Handles state changes and shows appropriate feedback
  void _handleStateChanges(BuildContext context, FaceVideoCaptureState state) {
    switch (state.runtimeType) {
      case Error:
        final errorState = state as Error;
        _logger.error(
          LoggingConstants.formatError(
            LoggingConstants.faceVerificationModule,
            LoggingConstants.criticalError,
            'Face capture error: ${errorState.error}',
          ),
        );

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${errorState.error}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: () {
                context.read<FaceVideoCaptureBloc>().add(const ResetCapture());
              },
            ),
          ),
        );

      case Success:
        final successState = state as Success;
        _logger.info(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Face verification completed successfully',
            'Quality score: ${successState.coverageStats.qualityScore.toStringAsFixed(1)}',
          ),
        );

      case Failure:
        final failureState = state as Failure;
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Face verification failed',
            'Reason: ${failureState.reason}',
          ),
        );

      default:
        break;
    }
  }

  /// Builds the main content based on current state
  Widget _buildContent(BuildContext context, FaceVideoCaptureState state) {
    switch (state.runtimeType) {
      case Initial:
      case CameraInitializing:
        return _buildLoadingScreen();

      case CameraReady:
        return _buildCameraReadyScreen(context, state);

      case CountdownInProgress:
        final countdownState = state as CountdownInProgress;
        return _buildCountdownScreen(context, countdownState);

      case Recording:
        final recordingState = state as Recording;
        return _buildRecordingScreen(context, recordingState);

      case Processing:
        return _buildProcessingScreen();

      case Success:
        final successState = state as Success;
        return _buildSuccessScreen(context, successState);

      case Failure:
        final failureState = state as Failure;
        return _buildFailureScreen(context, failureState);

      case Error:
        final errorState = state as Error;
        return _buildErrorScreen(context, errorState);

      default:
        return _buildLoadingScreen();
    }
  }

  /// Builds loading screen
  Widget _buildLoadingScreen() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
          SizedBox(height: 16),
          Text(
            'Initializing camera...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  /// Builds camera ready screen
  Widget _buildCameraReadyScreen(
    BuildContext context,
    FaceVideoCaptureState state,
  ) {
    return Stack(
      children: [
        // Camera preview
        const CameraPreviewWidget(),

        // Instructions and start button
        Positioned(
          bottom: 100,
          left: 20,
          right: 20,
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'Position your face in the center guide and tap Start Recording',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: () {
                  context
                      .read<FaceVideoCaptureBloc>()
                      .add(const StartCountdown());
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 16,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                ),
                child: const Text(
                  'Start Recording',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Back button
        Positioned(
          top: 20,
          left: 20,
          child: IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.arrow_back,
              color: Colors.white,
              size: 28,
            ),
          ),
        ),
      ],
    );
  }

  /// Builds countdown screen
  Widget _buildCountdownScreen(
    BuildContext context,
    CountdownInProgress state,
  ) {
    return Stack(
      children: [
        // Camera preview
        const CameraPreviewWidget(),

        // Countdown timer
        Center(
          child: CountdownTimerWidget(
            remainingSeconds: state.remainingSeconds,
          ),
        ),
      ],
    );
  }

  /// Builds recording screen
  Widget _buildRecordingScreen(
    BuildContext context,
    Recording state,
  ) {
    return Stack(
      children: [
        // Camera preview
        const CameraPreviewWidget(),

        // Recording feedback
        RecordingFeedbackWidget(
          elapsedTime: state.elapsedTime,
          remainingTime: state.remainingTime,
          progress: state.progress,
          currentDetection: state.currentDetection,
        ),
      ],
    );
  }

  /// Builds processing screen
  Widget _buildProcessingScreen() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
          SizedBox(height: 16),
          Text(
            'Processing video...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  /// Builds success screen
  Widget _buildSuccessScreen(BuildContext context, Success state) {
    return ResultScreenWidget(
      isSuccess: true,
      title: 'Verification Successful!',
      message: 'Your face verification video has been recorded successfully.',
      coverageStats: state.coverageStats,
      onRetry: () {
        context.read<FaceVideoCaptureBloc>().add(const ResetCapture());
      },
      onContinue: () {
        Navigator.of(context).pop(state.videoPath);
      },
    );
  }

  /// Builds failure screen
  Widget _buildFailureScreen(BuildContext context, Failure state) {
    return ResultScreenWidget(
      isSuccess: false,
      title: 'Verification Failed',
      message: state.reason,
      coverageStats: state.coverageStats,
      onRetry: () {
        context.read<FaceVideoCaptureBloc>().add(const ResetCapture());
      },
    );
  }

  /// Builds error screen
  Widget _buildErrorScreen(BuildContext context, Error state) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 16),
            const Text(
              'Camera Error',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              state.error,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                context
                    .read<FaceVideoCaptureBloc>()
                    .add(const InitializeCamera());
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }
}
