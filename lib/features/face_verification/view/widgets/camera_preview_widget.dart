import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/face_guide_overlay.dart';

/// {@template camera_preview_widget}
/// Widget that displays the camera preview with face detection overlay.
///
/// Shows a full-screen camera preview with a face guide overlay
/// and real-time face detection feedback.
/// {@endtemplate}
class CameraPreviewWidget extends StatelessWidget {
  /// {@macro camera_preview_widget}
  const CameraPreviewWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FaceVideoCaptureBloc, FaceVideoCaptureState>(
      builder: (context, state) {
        return Stack(
          children: [
            // Camera preview background
            _buildCameraPreview(context),

            // Face guide overlay
            FaceGuideOverlay(
              currentDetection: state.currentDetection,
              isRecording: state is Recording,
            ),
          ],
        );
      },
    );
  }

  /// Builds the camera preview
  Widget _buildCameraPreview(BuildContext context) {
    // In a real implementation, this would show the actual camera preview
    // using CamerAwesome or similar camera package

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF1A1A1A),
            Color(0xFF2D2D2D),
            Color(0xFF1A1A1A),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Mock camera preview with animated pattern
          _buildMockCameraPreview(),

          // Camera preview overlay effects
          _buildPreviewOverlay(),
        ],
      ),
    );
  }

  /// Builds a mock camera preview for demonstration
  Widget _buildMockCameraPreview() {
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: CustomPaint(
        painter: MockCameraPreviewPainter(),
      ),
    );
  }

  /// Builds preview overlay effects
  Widget _buildPreviewOverlay() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: RadialGradient(
          radius: 1.2,
          colors: [
            Colors.transparent,
            Colors.black.withOpacity(0.1),
            Colors.black.withOpacity(0.3),
          ],
          stops: const [0.0, 0.7, 1.0],
        ),
      ),
    );
  }
}

/// {@template mock_camera_preview_painter}
/// Custom painter that creates a mock camera preview with animated elements.
/// {@endtemplate}
class MockCameraPreviewPainter extends CustomPainter {
  /// {@macro mock_camera_preview_painter}
  MockCameraPreviewPainter();

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();

    // Draw background gradient
    const backgroundGradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        const Color(0xFF2A2A2A),
        const Color(0xFF1A1A1A),
        const Color(0xFF2A2A2A),
      ],
    );

    final backgroundRect = Rect.fromLTWH(0, 0, size.width, size.height);
    paint.shader = backgroundGradient.createShader(backgroundRect);
    canvas.drawRect(backgroundRect, paint);

    // Draw mock face silhouette in center
    _drawMockFaceSilhouette(canvas, size);

    // Draw scanning lines effect
    _drawScanningLines(canvas, size);
  }

  /// Draws a mock face silhouette
  void _drawMockFaceSilhouette(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.1)
      ..style = PaintingStyle.fill;

    final centerX = size.width / 2;
    final centerY = size.height / 2;

    // Draw oval face shape
    final faceRect = Rect.fromCenter(
      center: Offset(centerX, centerY - 20),
      width: size.width * 0.4,
      height: size.height * 0.5,
    );

    canvas.drawOval(faceRect, paint);

    // Draw facial features
    paint.color = Colors.white.withOpacity(0.05);

    // Eyes
    const eyeRadius = 8.0;
    canvas.drawCircle(
      Offset(centerX - 30, centerY - 40),
      eyeRadius,
      paint,
    );
    canvas.drawCircle(
      Offset(centerX + 30, centerY - 40),
      eyeRadius,
      paint,
    );

    // Nose
    final nosePath = Path();
    nosePath.moveTo(centerX, centerY - 10);
    nosePath.lineTo(centerX - 8, centerY + 10);
    nosePath.lineTo(centerX + 8, centerY + 10);
    nosePath.close();
    canvas.drawPath(nosePath, paint);

    // Mouth
    final mouthRect = Rect.fromCenter(
      center: Offset(centerX, centerY + 30),
      width: 40,
      height: 15,
    );
    canvas.drawOval(mouthRect, paint);
  }

  /// Draws animated scanning lines
  void _drawScanningLines(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.blue.withOpacity(0.3)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // Draw horizontal scanning lines
    for (var i = 0; i < 5; i++) {
      final y = (size.height / 6) * (i + 1);
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    // Draw vertical scanning lines
    for (var i = 0; i < 3; i++) {
      final x = (size.width / 4) * (i + 1);
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false; // Static preview for now
  }
}
